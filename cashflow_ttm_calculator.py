#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现金流TTM计算器

功能：从tushare获取股票现金流数据，计算过去十二个月的经营活动产生的现金流量之和（现金流TTM）

作者：AI助手
日期：2025-08-24
"""

import tushare as ts
import pandas as pd
from datetime import datetime

# 设置tushare token（请替换为您自己的token）
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

def calculate_cashflow_ttm(ts_code, end_date=None):
    """
    计算现金流TTM（过去十二个月的经营活动产生的现金流量之和）
    
    参数:
    ts_code: 股票代码，如 '000651.SZ'
    end_date: 截止日期，格式为 'YYYYMMDD'，默认为当前日期
    
    返回:
    现金流TTM值（单位：万元）
    """
    
    if end_date is None:
        end_date = datetime.now().strftime('%Y%m%d')
    
    print(f"正在获取 {ts_code} 的现金流数据...")
    
    try:
        # 获取现金流数据，重点关注经营活动现金流
        df = pro.cashflow(
            ts_code=ts_code,
            fields=[
                'ts_code', 'end_date', 'report_type', 'n_cashflow_act'  # n_cashflow_act是经营活动产生的现金流量净额
            ]
        )
        
        if df.empty:
            print(f"未找到 {ts_code} 的现金流数据")
            return None
        
        # 转换日期格式并排序
        df['end_date'] = pd.to_datetime(df['end_date'])
        df = df.sort_values('end_date', ascending=False)
        
        # 去除重复记录（基于end_date和report_type）
        df = df.drop_duplicates(subset=['end_date', 'report_type'], keep='first')
        
        # 只保留季报数据（report_type=1表示季报）
        df_quarterly = df[df['report_type'] == 1].copy()

        print(f"筛选后获取到 {len(df_quarterly)} 条季度现金流数据")

        if len(df_quarterly) == 0:
            # 如果没有季报数据，尝试使用所有数据
            print("没有找到季报数据，尝试使用所有报告类型的数据")
            df_quarterly = df.copy()
            print(f"使用所有数据后有 {len(df_quarterly)} 条记录")

        if len(df_quarterly) < 4:
            print(f"数据不足，只有 {len(df_quarterly)} 条季度记录，无法计算TTM")
            return None
        
        # 计算TTM：取最近4个季度的经营活动现金流之和
        recent_4_quarters = df_quarterly.head(4).copy()
        
        # 处理空值
        recent_4_quarters.loc[:, 'n_cashflow_act'] = recent_4_quarters['n_cashflow_act'].fillna(0)
        
        # 计算TTM
        cashflow_ttm = recent_4_quarters['n_cashflow_act'].sum()
        
        print(f"\n=== 现金流TTM计算结果 ===")
        print(f"股票代码: {ts_code}")
        print(f"计算日期: {end_date}")
        print(f"最近4个季度的经营活动现金流:")
        for _, row in recent_4_quarters.iterrows():
            print(f"  {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']:,.2f} 万元")
        print(f"现金流TTM: {cashflow_ttm:,.2f} 万元")
        print(f"现金流TTM: {cashflow_ttm/10000:.2f} 亿元")
        
        return cashflow_ttm
        
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        return None

def batch_calculate_cashflow_ttm(stock_list, end_date=None):
    """
    批量计算多只股票的现金流TTM
    
    参数:
    stock_list: 股票代码列表
    end_date: 截止日期
    
    返回:
    包含所有股票现金流TTM的DataFrame
    """
    results = []
    
    for ts_code in stock_list:
        try:
            ttm = calculate_cashflow_ttm(ts_code, end_date)
            results.append({
                'ts_code': ts_code,
                'cashflow_ttm_万元': ttm,
                'cashflow_ttm_亿元': ttm/10000 if ttm is not None else None,
                'calculation_date': end_date or datetime.now().strftime('%Y%m%d')
            })
            print(f"完成 {ts_code} 的计算\n" + "="*50)
        except Exception as e:
            print(f"计算 {ts_code} 时出错: {str(e)}")
            results.append({
                'ts_code': ts_code,
                'cashflow_ttm_万元': None,
                'cashflow_ttm_亿元': None,
                'calculation_date': end_date or datetime.now().strftime('%Y%m%d')
            })
    
    return pd.DataFrame(results)

def main():
    """主函数 - 演示如何使用现金流TTM计算器"""
    
    print("=== 现金流TTM计算器 ===")
    print("TTM = Trailing Twelve Months（过去十二个月）")
    print("现金流TTM = 过去十二个月的经营活动产生的现金流量之和\n")
    
    # 示例1：单只股票计算
    print("示例1：单只股票计算")
    stock_code = "000651.SZ"  # 格力电器
    ttm_result = calculate_cashflow_ttm(stock_code)
    
    print("\n" + "="*60 + "\n")
    
    # 示例2：批量计算
    print("示例2：批量计算多只股票")
    stock_list = [
        "000651.SZ",  # 格力电器
        "000858.SZ",  # 五粮液
        "002415.SZ"   # 海康威视
    ]
    
    batch_results = batch_calculate_cashflow_ttm(stock_list)
    
    print("\n=== 批量计算汇总结果 ===")
    print(batch_results.to_string(index=False))
    
    # 保存结果到CSV
    filename = f'cashflow_ttm_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    batch_results.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"\n结果已保存到 '{filename}' 文件中。")
    
    return batch_results

if __name__ == "__main__":
    results = main()
